@extends('layouts.app')

@section('title', 'Sugar Partner Exchange Status')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-heart-fill me-2"></i>Sugar Partner Exchange Status
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Exchange Overview -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="bi bi-person-circle fs-1 text-primary"></i>
                                    <h5>{{ $user->name }}</h5>
                                    <p class="text-muted mb-2">You</p>
                                    @if($userHasPaid)
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>Payment Complete
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="bi bi-clock me-1"></i>Payment Pending
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="bi bi-person-heart fs-1 text-danger"></i>
                                    <h5>{{ $otherUser->name }}</h5>
                                    <p class="text-muted mb-2">Exchange Partner</p>
                                    @if($otherUserHasPaid)
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>Payment Complete
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="bi bi-clock me-1"></i>Payment Pending
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Exchange Status -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-info-circle me-2"></i>Exchange Progress
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="mb-2">
                                            @if($bothUsersPaid)
                                                <i class="bi bi-check-circle-fill fs-1 text-success"></i>
                                            @elseif($anyUserPaid)
                                                <i class="bi bi-check-circle fs-1 text-primary"></i>
                                            @else
                                                <i class="bi bi-clock fs-1 text-warning"></i>
                                            @endif
                                        </div>
                                        <h6>Payment</h6>
                                        <p class="text-muted small">
                                            @if($bothUsersPaid)
                                                Both users paid
                                            @elseif($anyUserPaid)
                                                One user paid
                                            @else
                                                Waiting for payments
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="mb-2">
                                            @if($anyUserPaid)
                                                <i class="bi bi-eye-fill fs-1 text-primary"></i>
                                            @else
                                                <i class="bi bi-eye-slash fs-1 text-muted"></i>
                                            @endif
                                        </div>
                                        <h6>Profile Viewing</h6>
                                        <p class="text-muted small">
                                            @if($bothPaid)
                                                Available
                                            @else
                                                Locked
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="mb-2">
                                            @if($userResponse)
                                                <i class="bi bi-chat-heart-fill fs-1 text-success"></i>
                                            @elseif($bothPaid)
                                                <i class="bi bi-chat-heart fs-1 text-info"></i>
                                            @else
                                                <i class="bi bi-chat-heart fs-1 text-muted"></i>
                                            @endif
                                        </div>
                                        <h6>Response</h6>
                                        <p class="text-muted small">
                                            @if($userResponse)
                                                {{ $userResponse->getRejectionTypeDisplayName() }}
                                            @elseif($bothPaid)
                                                Pending
                                            @else
                                                Locked
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    @if($userPayment)
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-receipt me-2"></i>Your Payment Details
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Amount:</strong><br>
                                        {{ $exchange->currency }} {{ number_format($userPayment->amount, 2) }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Method:</strong><br>
                                        {{ $userPayment->getPaymentMethodDisplayName() }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Status:</strong><br>
                                        <span class="badge {{ $userPayment->getStatusBadgeClass() }}">
                                            {{ ucfirst($userPayment->status) }}
                                        </span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Paid At:</strong><br>
                                        {{ $userPayment->paid_at?->format('M d, Y h:i A') ?? 'N/A' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Action Buttons -->
                    <div class="d-flex gap-2 flex-wrap">
                        @if(!$userHasPaid && $exchange->status === 'pending_payment')
                            <a href="{{ route('sugar-partner.exchange.payment', $exchange) }}" class="btn btn-primary">
                                <i class="bi bi-credit-card me-1"></i>Complete Payment
                            </a>
                        @endif

                        @if($anyUserPaid)
                            <a href="{{ route('sugar-partner.exchange.profile', [$exchange, $otherUser]) }}" class="btn btn-success">
                                <i class="bi bi-eye me-1"></i>View {{ $otherUser->name }}'s Profile
                            </a>
                        @endif

                        @if($anyUserPaid && !$userResponse)
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#responseModal">
                                <i class="bi bi-chat-heart me-1"></i>Submit Response
                                @if(!$bothUsersPaid)
                                    <small class="d-block text-muted" style="font-size: 0.7em;">Notification sent when both users pay</small>
                                @endif
                            </button>
                        @endif

                        <a href="{{ route('home') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-house me-1"></i>Dashboard
                        </a>
                    </div>

                    <!-- Response Information -->
                    @if($userResponse)
                        <div class="alert alert-info mt-4">
                            <h6 class="alert-heading">
                                <i class="bi bi-check-circle me-2"></i>Your Response Submitted
                            </h6>
                            <p class="mb-1">
                                <strong>Response:</strong> {{ $userResponse->getRejectionTypeDisplayName() }}
                            </p>
                            @if($userResponse->rejection_reason)
                                <p class="mb-1">
                                    <strong>Reason:</strong> {{ $userResponse->rejection_reason }}
                                </p>
                            @endif
                            <p class="mb-0">
                                <strong>Submitted:</strong> {{ $userResponse->created_at->format('M d, Y h:i A') }}
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Response Modal -->
@if($bothPaid && !$userResponse)
<div class="modal fade" id="responseModal" tabindex="-1" aria-labelledby="responseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="responseModalLabel">
                    <i class="bi bi-chat-heart me-2"></i>Submit Your Response
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ route('sugar-partner.exchange.submit-response', $exchange) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Your Response to {{ $otherUser->name }}</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="rejection_type" id="accept" value="accept" required>
                            <label class="form-check-label text-success" for="accept">
                                <i class="bi bi-heart-fill me-1"></i><strong>Accept</strong> - Express interest in this match
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="rejection_type" id="soft_reject" value="soft_reject" required>
                            <label class="form-check-label text-warning" for="soft_reject">
                                <i class="bi bi-dash-circle me-1"></i><strong>Soft Reject</strong> - Decline with possibility of future matching
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="rejection_type" id="hard_reject" value="hard_reject" required>
                            <label class="form-check-label text-danger" for="hard_reject">
                                <i class="bi bi-x-circle me-1"></i><strong>Hard Reject</strong> - Permanent rejection (no future exchanges)
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">Reason (Optional)</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" placeholder="Share your thoughts..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="admin_note" class="form-label">Note for Admin (Private)</label>
                        <textarea class="form-control" id="admin_note" name="admin_note" rows="2" placeholder="Private feedback for admin (optional)"></textarea>
                        <div class="form-text">This note will only be visible to administrators.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-send me-1"></i>Submit Response
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endsection
